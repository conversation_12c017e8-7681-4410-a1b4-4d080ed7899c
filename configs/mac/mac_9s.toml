# Mac mini M4 optimized configuration for 9-second video generation
# Optimized for 16GB unified memory and Apple Silicon

[job]
exp_name = "mac-cogvideo-9s"

[model]
name = "cogvideo"
size = "5B"
ssm_layer = "ttt_linear"  # Linear is more memory efficient than MLP
ttt_base_lr = 0.1
mini_batch_size = 4      # Further reduced for longer videos
video_length = "9sec"
scale_factor = 1.0

[eval]
sampling_fps = 16
sampling_num_frames = 37
txt_maxlen = 256         # Reduced for memory efficiency
image_width = 480        # Reduced resolution for memory efficiency
image_height = 320       
latent_channels = 16
scale_factor = 0.7
num_denoising_steps = 20 # Reduced for memory and speed
output_dir = "./output"
dtype = "bfloat16"

# Model paths - update these to point to your WorkDrive
t5_model_dir = "/Volumes/WorkDrive/models/t5-v1_1-xxl"
vae_checkpoint_path = "/Volumes/WorkDrive/models/cogvideox-vae"

[guider]
scale = 6
exp = 5
num_steps = 20

[discretization]
shift_scale = 1.0

[denoiser]
num_idx = 1000
quantize_c_noise = false

[parallelism]
fsdp_unsharded_dtype = 'bfloat16'
dp_replicate = 1
dp_sharding = 1
tp_sharding = 1

[remat]
scan_checkpoint_group_size = 1e6

[checkpoint]
init_state_dir = "/Volumes/WorkDrive/models/cogvideox-5b-weights"

[wandb]
disable = true
project = "mac-cogvideo"
entity = "local"
log_interval = 1

[mac]
force_cpu_fallback = false
prefer_mps = true
memory_fraction = 0.7        # More conservative for longer videos
enable_optimizations = true
