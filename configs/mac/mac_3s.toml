# Mac mini M4 optimized configuration for 3-second video generation
# Optimized for 16GB unified memory and Apple Silicon

[job]
exp_name = "mac-cogvideo-3s"

[model]
name = "cogvideo"
size = "5B"
ssm_layer = "ttt_linear"  # Linear is more memory efficient than MLP
ttt_base_lr = 0.1
mini_batch_size = 8      # Reduced from 16 for memory efficiency
video_length = "3sec"
scale_factor = 1.0

[eval]
sampling_fps = 16
sampling_num_frames = 13
txt_maxlen = 256         # Reduced from 498 for memory efficiency
image_width = 480        # Reduced from 720 for memory efficiency  
image_height = 320       # Reduced from 480 for memory efficiency
latent_channels = 16
scale_factor = 0.7
num_denoising_steps = 25 # Reduced from 50 for faster inference
output_dir = "./output"
dtype = "bfloat16"       # Use bfloat16 for memory efficiency

# Model paths - update these to point to your WorkDrive
t5_model_dir = "/Volumes/WorkDrive/models/t5-v1_1-xxl"
vae_checkpoint_path = "/Volumes/WorkDrive/models/cogvideox-vae"

[guider]
scale = 6
exp = 5
num_steps = 25           # Match num_denoising_steps

[discretization]
shift_scale = 1.0

[denoiser]
num_idx = 1000
quantize_c_noise = false

[parallelism]
fsdp_unsharded_dtype = 'bfloat16'
dp_replicate = 1         # Single device
dp_sharding = 1          # Single device
tp_sharding = 1          # Single device

[remat]
scan_checkpoint_group_size = 1e6  # Avoid checkpoints during eval

[checkpoint]
init_state_dir = "/Volumes/WorkDrive/models/cogvideox-5b-weights"

[wandb]
disable = true           # Disable wandb for local inference
project = "mac-cogvideo"
entity = "local"
log_interval = 1

[mac]
# Mac-specific settings
force_cpu_fallback = false    # Use MPS if available
prefer_mps = true            # Prefer MPS over CPU
memory_fraction = 0.8        # Reserve 20% of memory
enable_optimizations = true  # Enable Mac-specific optimizations
