[job]
exp_name = "cogvideo-linear-63s"

[model]
name = "cogvideo"
size = "5B"
ssm_layer = "ttt_linear"
ttt_base_lr = 1.0
mini_batch_size = 16
video_length = "63sec"

[optimizer]
name = "AdamW"
lr = 1e-5
lr_ssm = 1e-5
lr_end = 1e-5
lr_schedule = "linear"
lr_ssm_schedule = "cosine"
gradient_clipping_norm = 0.1

[training]
global_batch_size = 64
grad_accum_steps = 1
warmup_steps = 25
steps = 250
adapter_method = "qkvo"
dataset_path = TODO
jsonl_paths = TODO

[remat]
transformer_checkpoint_layer_group_size = 2
scan_checkpoint_group_size = 16
forward_ssm = true
reverse_ssm = true
attention = true
mlp = true
seq_modeling_block = true
shard_transformer_inputs = true

[parallelism]
fsdp_unsharded_dtype = 'bfloat16'
dp_replicate = 16
dp_sharding = 4
tp_sharding = 4

[checkpoint]
interval = 25

[wandb]
project = "cogvideo_release"
entity = "ttt-vid"
log_interval = 1
