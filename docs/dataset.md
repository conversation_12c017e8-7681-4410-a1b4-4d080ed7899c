## Dataset Creation

This guide explains how to assemble datasets for multi-stage training.

### Overview

Our dataset creation involves three steps:
1. Annotators manually divide videos into separate scenes.
2. Scenes are cropped to multiples of 3 seconds. For example, a 10-second scene loses 0.5 seconds from both the start and end. Scenes shorter than 2 seconds are discarded.
3. Each scene is split into consecutive 3-second segments. These segments are concatenated to form longer video sequences.

This guide assumes you've completed the three steps above and shows you how to:
- Encode each 3-second video segment using the VAE.
- Tokenize text and use a special token to indicate scene boundaries.
- Assembling the JSONL file compatible with our dataloader.

## Computing Video Latents

Start with:
- `--precomp.episode_dir`: This directory contains subfolders, each corresponding to an "episode". Each subfolder includes video segments whose lengths are multiples of 3 seconds.

Run `data/precomp_video.py` to encode segments using the VAE. To avoid memory issues from 3D convolutions, the VAE processes videos in 1-second chunks.

## Computing Text Embeddings

Start with a JSONL-formatted dataset where each line has the following generalized structure:

```
{
  "scene_start": [bool, bool, ...],
  "scene_end": [bool, bool, ...],
  "text_0": "<Text Annotation>",
  "text_1": "<Text Annotation>",
  ...
  "text_n": "<Text Annotation>"
}
```

Run `data/precomp_text.py`, which iterates over all text annotations (text_0, text_1, ..., text_n) for each entry and generates embeddings using the T5 model.

## Creating Training JSONL

### Assemble the final training JSONL file, structured as follows:

Each line represents one data point and follows this generalized format:

```
{
  "vid_emb": "<video_embedding_path>",
  "text_chunk_emb": [
    "<text_annotation_embedding_path_1>",
    "<text_annotation_embedding_path_2>",
    "...",
    "<text_annotation_embedding_path_n>"
  ]
}
```

- The number of text annotation embeddings (n) should be equal to the video length divided by 3 seconds (assuming annotations every 3 seconds).
- Each embedding path corresponds to the precomputed embeddings generated by `data/precomp_text.py` from the associated text annotations.
- If the text for a 3-second segment is from a different scene, use the `<text_annotation_path` with the appropriate `<scene_token>` appended.
