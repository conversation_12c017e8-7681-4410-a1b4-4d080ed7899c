#!/usr/bin/env python3
"""
Storage setup script for Mac mini M4 with WorkDrive.
Creates directory structure and provides download instructions for required models.
"""

import os
import sys
from pathlib import Path


class MacStorageSetup:
    """Manages storage setup for Mac mini M4 with external drive."""
    
    def __init__(self, workdrive_path: str = "/Volumes/WorkDrive"):
        self.workdrive_path = Path(workdrive_path)
        self.models_dir = self.workdrive_path / "models"
        self.cache_dir = self.workdrive_path / "cache"
        self.output_dir = self.workdrive_path / "output"
        
    def check_workdrive(self) -> bool:
        """Check if WorkDrive is mounted."""
        if not self.workdrive_path.exists():
            print(f"❌ WorkDrive not found at {self.workdrive_path}")
            print("   Please ensure your external drive is connected and mounted as 'WorkDrive'")
            return False
        
        print(f"✅ WorkDrive found at {self.workdrive_path}")
        
        # Check available space
        stat = os.statvfs(str(self.workdrive_path))
        free_space_gb = (stat.f_bavail * stat.f_frsize) / (1024**3)
        print(f"   Available space: {free_space_gb:.1f} GB")
        
        if free_space_gb < 50:
            print("⚠️  Warning: Less than 50GB available. You may need more space for models.")
        
        return True
        
    def create_directories(self):
        """Create necessary directory structure."""
        directories = [
            self.models_dir / "cogvideox-5b-weights",
            self.models_dir / "cogvideox-vae", 
            self.models_dir / "t5-v1_1-xxl",
            self.cache_dir / "huggingface",
            self.cache_dir / "torch",
            self.output_dir / "videos",
            self.output_dir / "logs"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            print(f"📁 Created: {directory}")
            
    def setup_environment_variables(self):
        """Setup environment variables for caching."""
        env_vars = {
            "HF_HOME": str(self.cache_dir / "huggingface"),
            "TORCH_HOME": str(self.cache_dir / "torch"),
            "TRANSFORMERS_CACHE": str(self.cache_dir / "huggingface" / "transformers"),
        }
        
        # Create shell script for environment setup
        shell_script = self.workdrive_path / "setup_env.sh"
        with open(shell_script, 'w') as f:
            f.write("#!/bin/bash\n")
            f.write("# Environment setup for TTT-Video on Mac\n\n")
            for key, value in env_vars.items():
                f.write(f'export {key}="{value}"\n')
            f.write("\necho '✅ Environment variables set for TTT-Video'\n")
            
        shell_script.chmod(0o755)
        print(f"📝 Created environment setup script: {shell_script}")
        
        return env_vars
        
    def get_download_instructions(self) -> dict:
        """Get download instructions for required models."""
        instructions = {
            "CogVideoX-5B Transformer": {
                "url": "https://huggingface.co/THUDM/CogVideoX-5b/tree/main/transformer",
                "files": [
                    "diffusion_pytorch_model-00001-of-00002.safetensors",
                    "diffusion_pytorch_model-00002-of-00002.safetensors",
                    "config.json"
                ],
                "destination": str(self.models_dir / "cogvideox-5b-weights"),
                "size": "~10GB"
            },
            "CogVideoX VAE": {
                "url": "https://huggingface.co/THUDM/CogVideoX-5b/tree/main/vae",
                "files": [
                    "diffusion_pytorch_model.safetensors",
                    "config.json"
                ],
                "destination": str(self.models_dir / "cogvideox-vae"),
                "size": "~1GB"
            },
            "T5-XXL Encoder": {
                "url": "https://huggingface.co/google/t5-v1_1-xxl",
                "files": [
                    "pytorch_model-00001-of-00002.bin",
                    "pytorch_model-00002-of-00002.bin", 
                    "config.json",
                    "tokenizer.json",
                    "tokenizer_config.json"
                ],
                "destination": str(self.models_dir / "t5-v1_1-xxl"),
                "size": "~11GB"
            }
        }
        return instructions
        
    def print_download_instructions(self):
        """Print detailed download instructions."""
        instructions = self.get_download_instructions()
        
        print("\n" + "="*60)
        print("📥 MODEL DOWNLOAD INSTRUCTIONS")
        print("="*60)
        
        for model_name, info in instructions.items():
            print(f"\n🔹 {model_name} ({info['size']})")
            print(f"   URL: {info['url']}")
            print(f"   Destination: {info['destination']}")
            print("   Files to download:")
            for file in info['files']:
                print(f"     - {file}")
                
        print("\n" + "="*60)
        print("💡 DOWNLOAD METHODS")
        print("="*60)
        
        print("\n1. Using git-lfs (recommended):")
        print("   git lfs install")
        for model_name, info in instructions.items():
            repo_name = info['url'].split('/')[-1] if 'huggingface.co' in info['url'] else model_name
            print(f"   git clone https://huggingface.co/THUDM/{repo_name} {info['destination']}")
            
        print("\n2. Using huggingface-hub:")
        print("   pip install huggingface-hub")
        print("   python -c \"from huggingface_hub import snapshot_download; snapshot_download('THUDM/CogVideoX-5b', local_dir='/Volumes/WorkDrive/models/cogvideox-5b-weights')\"")
        
        print("\n3. Manual download:")
        print("   Visit the URLs above and download files manually to the specified destinations")
        
    def create_download_script(self):
        """Create automated download script."""
        script_path = self.workdrive_path / "download_models.py"
        
        script_content = '''#!/usr/bin/env python3
"""
Automated model download script for TTT-Video on Mac.
"""

import os
from pathlib import Path
from huggingface_hub import snapshot_download

def download_models():
    """Download all required models."""
    models = [
        {
            "repo_id": "THUDM/CogVideoX-5b",
            "local_dir": "/Volumes/WorkDrive/models/cogvideox-5b-weights",
            "allow_patterns": ["transformer/*"]
        },
        {
            "repo_id": "THUDM/CogVideoX-5b", 
            "local_dir": "/Volumes/WorkDrive/models/cogvideox-vae",
            "allow_patterns": ["vae/*"]
        },
        {
            "repo_id": "google/t5-v1_1-xxl",
            "local_dir": "/Volumes/WorkDrive/models/t5-v1_1-xxl"
        }
    ]
    
    for model in models:
        print(f"📥 Downloading {model['repo_id']}...")
        try:
            snapshot_download(**model)
            print(f"✅ Downloaded {model['repo_id']}")
        except Exception as e:
            print(f"❌ Failed to download {model['repo_id']}: {e}")

if __name__ == "__main__":
    print("🚀 Starting model download...")
    download_models()
    print("✅ All downloads completed!")
'''
        
        with open(script_path, 'w') as f:
            f.write(script_content)
            
        script_path.chmod(0o755)
        print(f"📝 Created download script: {script_path}")
        
    def setup(self):
        """Run complete setup process."""
        print("🍎 Setting up TTT-Video for Mac mini M4...")
        
        if not self.check_workdrive():
            return False
            
        print("\n📁 Creating directory structure...")
        self.create_directories()
        
        print("\n🔧 Setting up environment...")
        self.setup_environment_variables()
        
        print("\n📝 Creating download script...")
        self.create_download_script()
        
        self.print_download_instructions()
        
        print("\n" + "="*60)
        print("✅ SETUP COMPLETE!")
        print("="*60)
        print("\nNext steps:")
        print("1. Run the download script: python /Volumes/WorkDrive/download_models.py")
        print("2. Or manually download models using the instructions above")
        print("3. Source the environment: source /Volumes/WorkDrive/setup_env.sh")
        print("4. Run TTT-Video inference with Mac configs")
        
        return True


if __name__ == "__main__":
    setup = MacStorageSetup()
    setup.setup()
