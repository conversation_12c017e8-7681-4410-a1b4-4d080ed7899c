# TTT-Video for Mac mini M4

🍎 **Mac mini M4 优化版本** - 在Apple Silicon上本地运行TTT-Video视频生成

## 系统要求

- **硬件**: Mac mini M4 (16GB统一内存)
- **存储**: 1TB外置硬盘 (推荐命名为"WorkDrive")
- **系统**: macOS 14.0+ (支持Apple Silicon)
- **Python**: 3.12+

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone https://github.com/your-repo/ttt-video-dit.git
cd ttt-video-dit

# 创建conda环境 (推荐)
conda env create -f environment.yaml
conda activate ttt-video

# 或使用pip安装
pip install -e .
```

### 2. 存储设置

确保你的1TB移动硬盘已连接并命名为"WorkDrive"：

```bash
# 运行存储设置脚本
python setup_mac_storage.py
```

这将创建以下目录结构：
```
/Volumes/WorkDrive/
├── models/
│   ├── cogvideox-5b-weights/
│   ├── cogvideox-vae/
│   └── t5-v1_1-xxl/
├── cache/
│   ├── huggingface/
│   └── torch/
└── output/
    ├── videos/
    └── logs/
```

### 3. 模型下载

#### 方法1: 自动下载 (推荐)
```bash
# 安装huggingface-hub
pip install huggingface-hub

# 运行自动下载脚本
python /Volumes/WorkDrive/download_models.py
```

#### 方法2: 手动下载
访问以下链接手动下载模型文件：

1. **CogVideoX-5B Transformer** (~10GB)
   - URL: https://huggingface.co/THUDM/CogVideoX-5b/tree/main/transformer
   - 下载到: `/Volumes/WorkDrive/models/cogvideox-5b-weights/`

2. **CogVideoX VAE** (~1GB)
   - URL: https://huggingface.co/THUDM/CogVideoX-5b/tree/main/vae
   - 下载到: `/Volumes/WorkDrive/models/cogvideox-vae/`

3. **T5-XXL Encoder** (~11GB)
   - URL: https://huggingface.co/google/t5-v1_1-xxl
   - 下载到: `/Volumes/WorkDrive/models/t5-v1_1-xxl/`

### 4. 环境变量设置

```bash
# 设置环境变量
source /Volumes/WorkDrive/setup_env.sh
```

## 使用方法

### 基础推理

1. **准备输入文件**
```bash
# 创建提示词文件
echo '{"positive_prompt": "A cat playing with a ball", "negative_prompt": ""}' > input.json
```

2. **运行3秒视频生成**
```bash
python sample_mac.py \
    --job.config_file configs/mac/mac_3s.toml \
    --eval.input_file input.json \
    --eval.output_dir /Volumes/WorkDrive/output
```

3. **运行9秒视频生成**
```bash
python sample_mac.py \
    --job.config_file configs/mac/mac_9s.toml \
    --eval.input_file input.json \
    --eval.output_dir /Volumes/WorkDrive/output
```

### 高级配置

#### 内存优化
如果遇到内存不足，可以调整配置：

```bash
# 使用更小的batch size
python sample_mac.py \
    --job.config_file configs/mac/mac_3s.toml \
    --model.mini_batch_size 4 \
    --eval.image_width 320 \
    --eval.image_height 240 \
    --eval.input_file input.json
```

#### 强制CPU模式
如果MPS出现问题，可以强制使用CPU：

```bash
python sample_mac.py \
    --job.config_file configs/mac/mac_3s.toml \
    --mac.force_cpu_fallback true \
    --eval.input_file input.json
```

## 配置说明

### Mac优化配置特点

- **内存优化**: 减小batch size和分辨率
- **设备适配**: 自动检测并使用MPS或CPU
- **TTT层**: 使用CPU fallback，不依赖CUDA kernel
- **存储优化**: 模型文件存储在外置硬盘

### 配置文件对比

| 配置 | 3秒视频 | 9秒视频 |
|------|---------|---------|
| 帧数 | 13 | 37 |
| Mini Batch | 8 | 4 |
| 分辨率 | 480x320 | 480x320 |
| 去噪步数 | 25 | 20 |
| 内存使用 | ~8GB | ~12GB |

## 性能优化

### 1. 内存管理
```python
# 在代码中添加内存清理
import gc
torch.mps.empty_cache()  # 清理MPS缓存
gc.collect()             # Python垃圾回收
```

### 2. 批处理优化
- 3秒视频: batch_size=8
- 9秒视频: batch_size=4
- 更长视频: batch_size=2

### 3. 分辨率调整
根据内存情况调整输出分辨率：
- 高质量: 720x480
- 标准: 480x320  
- 节省内存: 320x240

## 故障排除

### 常见问题

1. **内存不足**
   ```
   解决方案: 减小batch_size或分辨率
   --model.mini_batch_size 2
   --eval.image_width 320 --eval.image_height 240
   ```

2. **MPS错误**
   ```
   解决方案: 强制使用CPU
   --mac.force_cpu_fallback true
   ```

3. **模型加载失败**
   ```
   检查模型文件路径和完整性
   ls -la /Volumes/WorkDrive/models/
   ```

4. **外置硬盘未挂载**
   ```
   确保硬盘已连接并命名为"WorkDrive"
   ls /Volumes/
   ```

### 性能监控

```bash
# 监控内存使用
python -c "
import psutil
print(f'内存使用: {psutil.virtual_memory().percent}%')
print(f'可用内存: {psutil.virtual_memory().available/1024**3:.1f}GB')
"

# 监控GPU使用 (如果使用MPS)
sudo powermetrics -n 1 -s gpu_power
```

## 预期性能

### Mac mini M4 (16GB) 性能指标

| 视频长度 | 分辨率 | 生成时间 | 内存峰值 |
|----------|--------|----------|----------|
| 3秒 | 480x320 | ~5-8分钟 | ~8GB |
| 9秒 | 480x320 | ~15-20分钟 | ~12GB |

*注意: 实际性能可能因系统负载和配置而异*

## 技术细节

### Mac适配特性

1. **设备管理**: 自动检测CUDA/MPS/CPU
2. **内存优化**: Apple统一内存架构优化
3. **TTT层**: CPU fallback实现，无需CUDA kernel
4. **分布式**: 简化为单设备模式
5. **存储**: 外置硬盘路径管理

### 架构差异

- **原版**: 依赖CUDA + H100 GPU
- **Mac版**: MPS/CPU + 内存优化 + 外置存储

## 更新日志

- **v1.0**: 初始Mac适配版本
- 支持MPS和CPU后端
- 优化内存使用
- 简化分布式代码
- 添加外置存储支持

## 贡献

欢迎提交Issue和Pull Request来改进Mac版本的兼容性和性能。

## 许可证

遵循原项目许可证。
