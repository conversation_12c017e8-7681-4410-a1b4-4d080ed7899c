"""
Single device parallelism module for Mac compatibility.
Provides simplified versions of distributed training functions for single device inference.
"""

import os
import torch
from typing import <PERSON>ple
from .device_manager import get_device_manager

# Torch dtype mapping
TORCH_DTYPE_MAP = {
    "float32": torch.float32,
    "bfloat16": torch.bfloat16,
    "float16": torch.float16,
}


class SingleDeviceConfig:
    """Configuration for single device mode."""
    
    def __init__(self, device_type: str = "auto", dtype: str = "bfloat16"):
        self.device_type = device_type
        self.dtype = dtype
        self.effective_rank = 0
        self.effective_world_size = 1


def init_single_device(job_config, force_cpu: bool = False):
    """
    Initialize single device mode (replacement for init_distributed).
    
    Args:
        job_config: Job configuration
        force_cpu: Force CPU usage
    """
    device_manager = get_device_manager(force_cpu=force_cpu)
    device, device_type = device_manager.get_optimal_device()
    
    # Setup device optimizations
    device_manager.setup_device_optimizations()
    
    # Set environment variables for single device mode
    os.environ["RANK"] = "0"
    os.environ["WORLD_SIZE"] = "1"
    os.environ["LOCAL_RANK"] = "0"
    
    print(f"🔧 Initialized single device mode: {device} ({device_type})")
    
    return device, device_type


def get_world_info(job_config) -> Tuple[int, int]:
    """
    Get world info for single device (replacement for distributed version).
    
    Returns:
        Tuple of (effective_rank, effective_world_size) - always (0, 1) for single device
    """
    return 0, 1


def end_distributed():
    """
    End distributed mode (no-op for single device).
    """
    pass


def apply_parallelisms(model, job_config):
    """
    Apply parallelisms for single device (simplified version).
    
    Args:
        model: The model to apply parallelisms to
        job_config: Job configuration
    """
    # For single device, we don't need FSDP or TP
    # Just ensure model is on the correct device
    device_manager = get_device_manager()
    device, device_type = device_manager.get_optimal_device()
    
    print(f"📱 Single device mode: skipping FSDP/TP, using {device}")
    
    # Set model to eval mode for inference
    model.eval()
    
    return model


def cast_rotary_freqs(model, dtype):
    """
    Cast rotary frequency buffers to specified dtype.
    
    Args:
        model: Model containing rotary frequency buffers
        dtype: Target dtype
    """
    def cast_freqs_recursive(module):
        for name, buffer in module.named_buffers():
            if 'freqs' in name.lower() and buffer is not None:
                buffer.data = buffer.data.to(dtype)
        
        for child in module.children():
            cast_freqs_recursive(child)
    
    cast_freqs_recursive(model)
    print(f"✅ Cast rotary frequencies to {dtype}")


def init_model_parameters(module):
    """
    Initialize model parameters (simplified version).
    
    Args:
        module: Module to initialize
    """
    if hasattr(module, 'init_weights'):
        module.init_weights()
    elif hasattr(module, 'weight') and module.weight is not None:
        if len(module.weight.shape) > 1:
            torch.nn.init.xavier_uniform_(module.weight)
        else:
            torch.nn.init.normal_(module.weight, std=0.02)
    
    if hasattr(module, 'bias') and module.bias is not None:
        torch.nn.init.zeros_(module.bias)


def get_model_state_dict(model):
    """
    Get model state dict (simplified version).
    
    Args:
        model: Model to get state dict from
        
    Returns:
        Model state dict
    """
    return model.state_dict()


def set_model_state_dict(model, model_state_dict, options=None):
    """
    Set model state dict (simplified version).
    
    Args:
        model: Model to set state dict for
        model_state_dict: State dict to load
        options: Load options (ignored in single device mode)
    """
    # Filter out any distributed-specific keys
    filtered_state_dict = {}
    for key, value in model_state_dict.items():
        # Remove any FSDP-specific prefixes
        clean_key = key.replace('_fsdp_wrapped_module.', '').replace('module.', '')
        filtered_state_dict[clean_key] = value
    
    # Load with strict=False to handle missing keys gracefully
    missing_keys, unexpected_keys = model.load_state_dict(filtered_state_dict, strict=False)
    
    if missing_keys:
        print(f"⚠️  Missing keys in state dict: {len(missing_keys)} keys")
    if unexpected_keys:
        print(f"⚠️  Unexpected keys in state dict: {len(unexpected_keys)} keys")
    
    print("✅ Model state dict loaded successfully")


class StateDictOptions:
    """Simple state dict options class."""
    
    def __init__(self, strict: bool = True):
        self.strict = strict


# Compatibility functions for existing code
def get_device_from_config(job_config):
    """Get device string from job config."""
    device_manager = get_device_manager()
    device, _ = device_manager.get_optimal_device()
    return device


def setup_memory_optimizations():
    """Setup memory optimizations for the current device."""
    device_manager = get_device_manager()
    device, device_type = device_manager.get_optimal_device()
    
    if device_type == "mps":
        # MPS-specific optimizations
        torch.mps.set_per_process_memory_fraction(0.8)  # Reserve some memory
        print("🍎 MPS memory optimizations enabled")
    elif device_type == "cuda":
        # CUDA-specific optimizations
        torch.cuda.empty_cache()
        print("🚀 CUDA memory optimizations enabled")
    else:
        # CPU optimizations
        torch.set_num_threads(min(8, torch.get_num_threads()))
        print(f"🖥️  CPU optimizations enabled")
