"""
Mac-compatible device management for TTT-Video.
Handles device detection and initialization for CUDA, MPS, and CPU backends.
"""

import os
import platform
import torch
from typing import Tu<PERSON>, Optional


class DeviceManager:
    """Manages device detection and initialization across different platforms."""
    
    def __init__(self, prefer_mps: bool = True, force_cpu: bool = False):
        """
        Initialize device manager.
        
        Args:
            prefer_mps: Whether to prefer MPS over CPU on Apple Silicon
            force_cpu: Force CPU usage even if GPU is available
        """
        self.prefer_mps = prefer_mps
        self.force_cpu = force_cpu
        self._device = None
        self._device_type = None
        
    def get_optimal_device(self) -> Tuple[str, str]:
        """
        Get the optimal device for the current platform.
        
        Returns:
            Tuple of (device_string, device_type)
            device_type can be 'cuda', 'mps', or 'cpu'
        """
        if self._device is not None:
            return self._device, self._device_type
            
        if self.force_cpu:
            self._device = "cpu"
            self._device_type = "cpu"
            print("🖥️  Using CPU (forced)")
            return self._device, self._device_type
            
        # Check for CUDA
        if torch.cuda.is_available():
            self._device = "cuda"
            self._device_type = "cuda"
            print(f"🚀 Using CUDA: {torch.cuda.get_device_name()}")
            return self._device, self._device_type
            
        # Check for MPS (Apple Silicon)
        if torch.backends.mps.is_available() and self.prefer_mps:
            self._device = "mps"
            self._device_type = "mps"
            print("🍎 Using Apple MPS (Metal Performance Shaders)")
            return self._device, self._device_type
            
        # Fallback to CPU
        self._device = "cpu"
        self._device_type = "cpu"
        print("🖥️  Using CPU")
        return self._device, self._device_type
        
    def is_apple_silicon(self) -> bool:
        """Check if running on Apple Silicon."""
        return platform.system() == "Darwin" and platform.machine() == "arm64"
        
    def get_memory_info(self) -> dict:
        """Get memory information for the current device."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "cuda":
            return {
                "total": torch.cuda.get_device_properties(device).total_memory,
                "allocated": torch.cuda.memory_allocated(device),
                "cached": torch.cuda.memory_reserved(device)
            }
        elif device_type == "mps":
            # MPS doesn't have direct memory query, estimate based on system
            import psutil
            return {
                "total": psutil.virtual_memory().total,
                "allocated": 0,  # Not available for MPS
                "cached": 0     # Not available for MPS
            }
        else:  # CPU
            import psutil
            return {
                "total": psutil.virtual_memory().total,
                "allocated": psutil.virtual_memory().used,
                "cached": 0
            }
            
    def setup_device_optimizations(self):
        """Setup device-specific optimizations."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "cuda":
            # CUDA optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            torch.backends.cuda.matmul.allow_tf32 = True
            print("✅ CUDA optimizations enabled")
            
        elif device_type == "mps":
            # MPS optimizations
            # Enable memory efficient attention if available
            if hasattr(torch.backends.mps, 'enable_memory_efficient_attention'):
                torch.backends.mps.enable_memory_efficient_attention(True)
            print("✅ MPS optimizations enabled")
            
        else:  # CPU
            # CPU optimizations
            torch.set_num_threads(min(8, torch.get_num_threads()))
            print(f"✅ CPU optimizations enabled (threads: {torch.get_num_threads()})")
            
    def get_autocast_context(self, enabled: bool = True):
        """Get appropriate autocast context for the device."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "cuda":
            return torch.cuda.amp.autocast(enabled=enabled, dtype=torch.bfloat16)
        elif device_type == "mps":
            # MPS doesn't support autocast yet, use no-op context
            from contextlib import nullcontext
            return nullcontext()
        else:  # CPU
            return torch.cpu.amp.autocast(enabled=enabled, dtype=torch.bfloat16)
            
    def move_to_device(self, tensor_or_model, non_blocking: bool = False):
        """Move tensor or model to the optimal device."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "mps":
            # MPS doesn't support non_blocking
            return tensor_or_model.to(device)
        else:
            return tensor_or_model.to(device, non_blocking=non_blocking)
            
    def empty_cache(self):
        """Empty device cache if supported."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "cuda":
            torch.cuda.empty_cache()
        elif device_type == "mps":
            torch.mps.empty_cache()
        # CPU doesn't need cache clearing
        
    def synchronize(self):
        """Synchronize device if needed."""
        device, device_type = self.get_optimal_device()
        
        if device_type == "cuda":
            torch.cuda.synchronize()
        elif device_type == "mps":
            torch.mps.synchronize()
        # CPU doesn't need synchronization


# Global device manager instance
_device_manager = None


def get_device_manager(prefer_mps: bool = True, force_cpu: bool = False) -> DeviceManager:
    """Get or create global device manager instance."""
    global _device_manager
    if _device_manager is None:
        _device_manager = DeviceManager(prefer_mps=prefer_mps, force_cpu=force_cpu)
    return _device_manager


def get_optimal_device() -> Tuple[str, str]:
    """Convenience function to get optimal device."""
    return get_device_manager().get_optimal_device()


def setup_device_optimizations():
    """Convenience function to setup device optimizations."""
    return get_device_manager().setup_device_optimizations()
