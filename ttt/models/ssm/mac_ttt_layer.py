"""
Mac-compatible TTT layer implementation.
Forces CPU fallback mode and handles device-specific optimizations.
"""

import torch
import torch.nn as nn
from torch.distributed.device_mesh import Devi<PERSON><PERSON><PERSON>
from typing import Optional

from ttt.models.configs import ModelConfig
from ttt.models.ssm.ttt_layer import TTTBase, TTTWrapper
from ttt.models.ssm.ops import ttt_linear, ttt_mlp
from ttt.infra.device_manager import get_device_manager


class MacTTTLinear(TTTBase):
    """Mac-compatible TTT Linear layer with forced CPU fallback."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        self.W1 = nn.Parameter(torch.normal(0, 0.02, size=(self.num_heads, self.head_dim, self.head_dim)))
        self.b1 = nn.Parameter(torch.zeros(self.num_heads, 1, self.head_dim))
        
        # Force CPU fallback for Mac compatibility
        self.use_kernel = False
        print("🍎 TTTLinear: Using CPU fallback (Mac mode)")

    def init_weights(self):
        super().init_weights()
        nn.init.normal_(self.W1, mean=0.0, std=0.02)
        nn.init.zeros_(self.b1)

    def init_device_mesh(self, tp_mesh: DeviceMesh):
        """Skip tensor parallel initialization for single device mode."""
        print("📱 Skipping tensor parallel initialization (single device mode)")
        pass

    def ttt(self, inputs):
        """TTT computation using CPU fallback."""
        B = inputs["XV"].shape[0]
        L = inputs["XV"].shape[2] * inputs["XV"].shape[3]
        num_mini_batch = inputs["XV"].shape[2]

        W1_states = torch.tile(self.W1.unsqueeze(0), dims=(B, 1, 1, 1))
        b1_states = torch.tile(self.b1.unsqueeze(0), dims=(B, 1, 1, 1))

        checkpoint_group_size = min(max(self.config.scan_checkpoint_group_size, 1), num_mini_batch)

        # Always use CPU fallback
        XQW_batch = ttt_linear(
            inputs["XK"],
            inputs["XQ"],
            inputs["XV"],
            inputs["eta"],
            self.ttt_norm_weight,
            self.ttt_norm_bias,
            W1_states,
            b1_states,
            checkpoint_group_size,
        )

        XQW_batch = XQW_batch.reshape(B, L, self.width)
        return XQW_batch


class MacTTTMLP(TTTBase):
    """Mac-compatible TTT MLP layer with forced CPU fallback."""
    
    def __init__(self, config: ModelConfig):
        super().__init__(config)
        
        expansion_dim = 4 * self.head_dim
        self.W1 = nn.Parameter(torch.normal(0, 0.02, size=(self.num_heads, self.head_dim, expansion_dim)))
        self.b1 = nn.Parameter(torch.zeros(self.num_heads, 1, expansion_dim))
        self.W2 = nn.Parameter(torch.normal(0, 0.02, size=(self.num_heads, expansion_dim, self.head_dim)))
        self.b2 = nn.Parameter(torch.zeros(self.num_heads, 1, self.head_dim))
        
        # Force CPU fallback for Mac compatibility
        self.use_kernel = False
        print("🍎 TTTMLP: Using CPU fallback (Mac mode)")

    def init_weights(self):
        super().init_weights()
        nn.init.normal_(self.W1, mean=0.0, std=0.02)
        nn.init.zeros_(self.b1)
        nn.init.normal_(self.W2, mean=0.0, std=0.02)
        nn.init.zeros_(self.b2)

    def init_device_mesh(self, tp_mesh: DeviceMesh):
        """Skip tensor parallel initialization for single device mode."""
        print("📱 Skipping tensor parallel initialization (single device mode)")
        pass

    def ttt(self, inputs):
        """TTT computation using CPU fallback."""
        B = inputs["XV"].shape[0]
        L = inputs["XV"].shape[2] * inputs["XV"].shape[3]
        num_mini_batch = inputs["XV"].shape[2]

        W1_states = torch.tile(self.W1.unsqueeze(0), dims=(B, 1, 1, 1))
        b1_states = torch.tile(self.b1.unsqueeze(0), dims=(B, 1, 1, 1))
        W2_states = torch.tile(self.W2.unsqueeze(0), dims=(B, 1, 1, 1))
        b2_states = torch.tile(self.b2.unsqueeze(0), dims=(B, 1, 1, 1))

        checkpoint_group_size = min(max(self.config.scan_checkpoint_group_size, 1), num_mini_batch)

        # Always use CPU fallback
        XQW_batch = ttt_mlp(
            inputs["XK"],
            inputs["XQ"],
            inputs["XV"],
            inputs["eta"],
            self.ttt_norm_weight,
            self.ttt_norm_bias,
            W1_states,
            b1_states,
            W2_states,
            b2_states,
            checkpoint_group_size,
        )

        XQW_batch = XQW_batch.reshape(B, L, self.width)
        return XQW_batch


class MacTTTWrapper(nn.Module):
    """Mac-compatible TTT wrapper that uses CPU fallback layers."""
    
    def __init__(self, config: ModelConfig):
        super().__init__()

        self.model_dim = config.model_dim
        self.num_heads = config.num_heads
        self.rope_theta = config.rope_theta
        self.latent_height = config.latent_height
        self.latent_width = config.latent_width
        self.compressed_num_frames = config.compressed_num_frames

        # Use Mac-compatible TTT layers
        if config.ssm_layer == "ttt_linear":
            self.ttt = MacTTTLinear(config)
            print("🍎 Using MacTTTLinear")
        elif config.ssm_layer == "ttt_mlp":
            self.ttt = MacTTTMLP(config)
            print("🍎 Using MacTTTMLP")
        else:
            raise TypeError(f"No Mac-compatible TTT layer of type {config.ssm_layer}")

        self.register_buffer("freqs_cis", self._precompute_freqs_cis_3d(), persistent=False)

    def _precompute_freqs_cis_3d(self):
        """Precompute 3D rotary frequencies."""
        from ttt.models.ssm.utils import precompute_freqs_cis_3d
        
        return precompute_freqs_cis_3d(
            dim=self.model_dim // self.num_heads,
            height=self.latent_height,
            width=self.latent_width,
            num_frames=self.compressed_num_frames,
            theta=self.rope_theta,
        )

    def init_freqs(self):
        """Initialize frequency buffers on the correct device."""
        device_manager = get_device_manager()
        device, _ = device_manager.get_optimal_device()
        
        if self.freqs_cis.device != torch.device(device):
            self.freqs_cis = self.freqs_cis.to(device)
            print(f"✅ Moved freqs_cis to {device}")

    def forward(self, hidden_states, freqs_cis, seq_metadata):
        """Forward pass through Mac-compatible TTT layer."""
        return self.ttt(hidden_states, freqs_cis, seq_metadata)


def create_mac_ttt_wrapper(config: ModelConfig) -> MacTTTWrapper:
    """
    Factory function to create Mac-compatible TTT wrapper.
    
    Args:
        config: Model configuration
        
    Returns:
        Mac-compatible TTT wrapper
    """
    wrapper = MacTTTWrapper(config)
    
    # Move to optimal device
    device_manager = get_device_manager()
    device, device_type = device_manager.get_optimal_device()
    wrapper = wrapper.to(device)
    
    print(f"✅ Created Mac TTT wrapper on {device} ({device_type})")
    return wrapper


def check_ttt_compatibility():
    """Check if TTT layers can run on the current device."""
    device_manager = get_device_manager()
    device, device_type = device_manager.get_optimal_device()
    
    print(f"🔍 TTT Compatibility Check:")
    print(f"   Device: {device} ({device_type})")
    print(f"   CPU Fallback: ✅ Available")
    print(f"   CUDA Kernel: ❌ Disabled (Mac mode)")
    
    if device_type == "mps":
        print(f"   MPS Support: ✅ Using Apple Metal")
    elif device_type == "cpu":
        print(f"   CPU Threads: {torch.get_num_threads()}")
    
    return True
