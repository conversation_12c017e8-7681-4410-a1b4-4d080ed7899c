/**
 * @file
 * @brief An aggregate header of warp memory operations on tiles, where a single warp loads or stores data on its own.
 */

#pragma once

#include "shared_to_register.cuh"
#include "global_to_register.cuh"
#include "global_to_shared.cuh"

#include "complex/complex_shared_to_register.cuh"
#include "complex/complex_global_to_register.cuh"
#include "complex/complex_global_to_shared.cuh"

#ifdef KITTENS_HOPPER
#include "tma.cuh"
#endif