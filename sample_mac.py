#!/usr/bin/env python3
"""
Mac-compatible video generation script for TTT-Video.
Optimized for Mac mini M4 with Apple Silicon and external storage.
"""

import os
import sys
import gc
from typing import List
from pathlib import Path

import imageio
import numpy as np
import torch
from einops import rearrange
from tqdm import tqdm
from transformers import T5EncoderModel, T5Tokenizer

# Import Mac-compatible modules
from ttt.infra.config_manager import JobConfig
from ttt.infra.device_manager import get_device_manager, setup_device_optimizations
from ttt.infra.single_device_parallelisms import (
    TORCH_DTYPE_MAP, 
    init_single_device,
    get_world_info,
    apply_parallelisms,
    cast_rotary_freqs,
    get_model_state_dict,
    set_model_state_dict,
    StateDictOptions
)
from ttt.infra.utils import set_random_seed, display_logo
from ttt.models.cogvideo.model import CogVideoX
from ttt.models.cogvideo.sampler import DenoiserSampler, TextEncoder, PromptManager
from ttt.models.vae.autoencoder import VideoAutoencoderInferenceWrapper
from ttt.models.configs import ModelConfig
from ttt.models.vae.configs import VaeModelConfig


class MacVideoGenerator:
    """Mac-optimized video generator."""

    def __init__(
        self,
        model: CogVideoX,
        vae_model: VideoAutoencoderInferenceWrapper,
        tokenizer: T5Tokenizer,
        t5_encoder: T5EncoderModel,
        job_config: JobConfig,
        device_manager,
    ):
        """Initialize the Mac video generator."""
        self.model = model
        self.vae_model = vae_model
        self.tokenizer = tokenizer
        self.t5_encoder = t5_encoder
        self.job_config = job_config
        self.device_manager = device_manager
        
        device, device_type = device_manager.get_optimal_device()
        self.device = device
        self.device_type = device_type

        effective_rank, _ = get_world_info(job_config)

        self.sampler = DenoiserSampler(
            model=model.dit,
            config=job_config,
            device=device,
            use_wandb=False,  # Disabled for Mac
            effective_rank=effective_rank,
            seed=job_config.job.seed,
            dtype=TORCH_DTYPE_MAP[job_config.eval.dtype],
        )

        self.dtype = TORCH_DTYPE_MAP[job_config.eval.dtype]
        print(f"🎬 Mac video generator initialized on {device} ({device_type})")

    def generate_video(self, prompts: List[str], neg_prompts: List[str]) -> torch.Tensor:
        """Generate a video based on text prompts."""
        print(f"🎯 Generating video for: {prompts[0][:50]}...")
        
        # Encode text prompts
        with self.device_manager.get_autocast_context():
            text_emb = TextEncoder.encode_text(
                self.tokenizer, self.t5_encoder, prompts, self.device, 
                self.job_config.eval.txt_maxlen, self.dtype
            )

            neg_prompts = [txt or "" for txt in neg_prompts]
            neg_emb = TextEncoder.encode_text(
                self.tokenizer, self.t5_encoder, neg_prompts, self.device,
                self.job_config.eval.txt_maxlen, self.dtype
            )

        assert text_emb.ndim == neg_emb.ndim, "Positive and negative prompts must have the same shape"

        T, C, H, W, F = (
            self.job_config.eval.sampling_num_frames,
            self.job_config.eval.latent_channels,
            self.job_config.eval.image_height,
            self.job_config.eval.image_width,
            8,  # Factor for height and width scaling
        )

        print(f"📐 Generating latents: {T}x{C}x{H//F}x{W//F}")
        
        # Generate latents
        with self.device_manager.get_autocast_context():
            latents = self.sampler.sample(
                text_emb=text_emb, 
                neg_emb=neg_emb, 
                shape=(T, C, H // F, W // F), 
                batch_size=1
            )

        # Clear cache after sampling
        self.device_manager.empty_cache()
        gc.collect()

        print("🎨 Decoding latents to video frames...")
        
        # Decode latents to frames
        latents = rearrange(latents, "b t c h w -> b c t h w")
        
        with self.device_manager.get_autocast_context():
            decoded_frames = self.vae_model.decode_first_stage(latents)
            
        decoded_frames = rearrange(decoded_frames, "b c t h w -> b t c h w")

        # Normalize frames
        samples = torch.clamp((decoded_frames + 1.0) / 2.0, min=0.0, max=1.0).cpu()
        
        # Final cleanup
        self.device_manager.empty_cache()
        gc.collect()

        print("✅ Video generation complete!")
        return samples


class MacModelLoader:
    """Mac-optimized model loader."""
    
    @staticmethod
    def load_t5_encoder(job_config: JobConfig, device_manager):
        """Load T5 encoder optimized for Mac."""
        device, device_type = device_manager.get_optimal_device()
        
        print(f"📚 Loading T5 encoder on {device}...")
        
        tokenizer = T5Tokenizer.from_pretrained(
            job_config.eval.t5_model_dir,
            legacy=False,
            local_files_only=True
        )
        
        encoder = T5EncoderModel.from_pretrained(
            job_config.eval.t5_model_dir,
            torch_dtype=TORCH_DTYPE_MAP[job_config.eval.dtype],
            local_files_only=True
        )
        
        encoder = device_manager.move_to_device(encoder)
        encoder.eval()
        
        print(f"✅ T5 encoder loaded on {device}")
        return tokenizer, encoder

    @staticmethod
    def load_cogvideox_model(job_config: JobConfig, device_manager) -> CogVideoX:
        """Load CogVideoX model optimized for Mac."""
        device, device_type = device_manager.get_optimal_device()
        
        print(f"🎬 Loading CogVideoX model on {device}...")
        
        model_config = ModelConfig.get_preset(
            job_config.model.size, 
            job_config.model.video_length, 
            job_config
        )

        # Force CPU fallback for TTT layers
        model_config.ssm_layer = job_config.model.ssm_layer
        
        with torch.device("meta"):
            model = CogVideoX(model_config, effective_rank=0, effective_world_size=1)

        # Cast rotary freqs to model dtype
        cast_rotary_freqs(model, TORCH_DTYPE_MAP[job_config.parallelism.fsdp_unsharded_dtype])

        # Apply simplified parallelisms (no-op for single device)
        apply_parallelisms(model, job_config)

        # Move to device
        model = device_manager.move_to_device(model)
        model.init_ssm_weights()

        # Load checkpoint
        print(f"📦 Loading checkpoint from {job_config.checkpoint.init_state_dir}")
        try:
            import torch.distributed.checkpoint as dcp
            state_dict = get_model_state_dict(model)
            dcp.load(state_dict=state_dict, checkpoint_id=job_config.checkpoint.init_state_dir)
            set_model_state_dict(model, model_state_dict=state_dict, options=StateDictOptions(strict=False))
        except Exception as e:
            print(f"⚠️  Checkpoint loading failed: {e}")
            print("   Continuing with random weights...")

        model.eval()
        print(f"✅ CogVideoX model loaded on {device}")
        return model

    @staticmethod
    def load_vae_model(job_config: JobConfig, device_manager) -> VideoAutoencoderInferenceWrapper:
        """Load VAE model optimized for Mac."""
        device, device_type = device_manager.get_optimal_device()
        
        print(f"🎨 Loading VAE model on {device}...")
        
        vae_model = VideoAutoencoderInferenceWrapper(
            job_config.eval.vae_checkpoint_path,
            VaeModelConfig.get_encoder_config(),
            VaeModelConfig.get_decoder_config(),
            scale_factor=job_config.eval.vae_scale_factor,
        )
        
        vae_model = device_manager.move_to_device(vae_model)
        vae_model = vae_model.to(TORCH_DTYPE_MAP[job_config.eval.dtype])
        vae_model.eval()
        
        print(f"✅ VAE model loaded on {device}")
        return vae_model


class MacVideoSaver:
    """Mac-optimized video saver."""

    @staticmethod
    def save_video(video_batch: torch.Tensor, save_path: str, fps: int, prompts: List[str]) -> None:
        """Save video batch to disk."""
        os.makedirs(save_path, exist_ok=True)

        for i, video_tensor in enumerate(video_batch):
            gif_frames = [MacVideoSaver._prepare_frame(frame) for frame in video_tensor]

            video_file_path = os.path.join(save_path, f"{i:06d}.mp4")

            with imageio.get_writer(video_file_path, fps=fps) as writer:
                for frame in gif_frames:
                    writer.append_data(frame)

        # Save prompts
        prompt_file_path = os.path.join(save_path, "prompt.txt")
        prompt_text = "\n\n".join(prompts) if len(prompts) > 1 else prompts[0]

        with open(prompt_file_path, "w", encoding="utf-8") as prompt_file:
            prompt_file.write(prompt_text)
            
        print(f"💾 Video saved to: {video_file_path}")

    @staticmethod
    def _prepare_frame(frame: torch.Tensor) -> np.ndarray:
        """Convert tensor frame to numpy array."""
        frame = frame.to(torch.float32)
        frame = rearrange(frame, "c h w -> h w c")
        return (255.0 * frame).cpu().numpy().astype(np.uint8)


def main():
    """Main entry point for Mac video generation."""
    display_logo()
    print("🍎 TTT-Video for Mac mini M4")
    print("="*50)
    
    # Parse config
    config = JobConfig(eval_mode=True)
    config.parse_args()
    
    # Initialize device manager
    force_cpu = getattr(config, 'mac', {}).get('force_cpu_fallback', False)
    device_manager = get_device_manager(force_cpu=force_cpu)
    
    # Setup device and optimizations
    device, device_type = init_single_device(config, force_cpu=force_cpu)
    setup_device_optimizations()
    
    # Set random seed
    set_random_seed(config.job.seed)
    
    # Load models
    print("\n🔄 Loading models...")
    tokenizer, t5_encoder = MacModelLoader.load_t5_encoder(config, device_manager)
    model = MacModelLoader.load_cogvideox_model(config, device_manager)
    vae_model = MacModelLoader.load_vae_model(config, device_manager)
    
    # Create video generator
    video_generator = MacVideoGenerator(
        model=model,
        vae_model=vae_model,
        tokenizer=tokenizer,
        t5_encoder=t5_encoder,
        job_config=config,
        device_manager=device_manager,
    )
    
    # Get prompts
    if not config.eval.input_file:
        print("❌ No input file specified. Use --eval.input_file to specify prompts.")
        sys.exit(1)
        
    prompts = PromptManager.get_prompts(input_file=config.eval.input_file)
    save_path = os.path.join(config.eval.output_dir, config.job.exp_name)
    
    print(f"\n🎬 Processing {len(prompts)} prompts...")
    
    # Process prompts
    for i, (pos_prompts, neg_prompts) in enumerate(tqdm(prompts, desc="Generating videos")):
        print(f"\n📝 Prompt {i+1}/{len(prompts)}: {pos_prompts[0][:50]}...")
        
        # Generate video
        samples = video_generator.generate_video(pos_prompts, neg_prompts)
        
        # Save video
        output_path = os.path.join(save_path, f"prompt-{i}")
        MacVideoSaver.save_video(
            video_batch=samples,
            save_path=output_path,
            fps=config.eval.sampling_fps,
            prompts=pos_prompts,
        )
        
        # Memory cleanup
        device_manager.empty_cache()
        gc.collect()
    
    print(f"\n✅ All videos generated! Check: {save_path}")


if __name__ == "__main__":
    main()
