#!/usr/bin/env python3
"""
Test script to verify Mac TTT-Video setup.
Checks dependencies, device availability, and model paths.
"""

import os
import sys
import platform
from pathlib import Path

def check_system():
    """Check system requirements."""
    print("🔍 System Check")
    print("=" * 40)
    
    # Check macOS
    if platform.system() != "Darwin":
        print("❌ Not running on macOS")
        return False
    print(f"✅ macOS {platform.mac_ver()[0]}")
    
    # Check Apple Silicon
    if platform.machine() == "arm64":
        print("✅ Apple Silicon (ARM64)")
    else:
        print("⚠️  Intel Mac (x86_64) - MPS may not be available")
    
    # Check Python version
    python_version = sys.version_info
    if python_version >= (3, 12):
        print(f"✅ Python {python_version.major}.{python_version.minor}")
    else:
        print(f"❌ Python {python_version.major}.{python_version.minor} - Need 3.12+")
        return False
    
    return True


def check_dependencies():
    """Check required Python packages."""
    print("\n📦 Dependency Check")
    print("=" * 40)
    
    required_packages = [
        "torch",
        "torchvision", 
        "transformers",
        "einops",
        "imageio",
        "tqdm",
        "safetensors",
        "pillow"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def check_torch_backends():
    """Check PyTorch backend availability."""
    print("\n🔥 PyTorch Backend Check")
    print("=" * 40)
    
    import torch
    
    print(f"PyTorch version: {torch.__version__}")
    
    # Check CUDA
    if torch.cuda.is_available():
        print("✅ CUDA available")
        print(f"   Device: {torch.cuda.get_device_name()}")
    else:
        print("❌ CUDA not available")
    
    # Check MPS
    if torch.backends.mps.is_available():
        print("✅ MPS (Metal Performance Shaders) available")
        try:
            # Test MPS functionality
            x = torch.randn(10, 10).to("mps")
            y = torch.mm(x, x.t())
            print("✅ MPS functionality test passed")
        except Exception as e:
            print(f"⚠️  MPS test failed: {e}")
    else:
        print("❌ MPS not available")
    
    # CPU is always available
    print("✅ CPU backend available")
    print(f"   Threads: {torch.get_num_threads()}")
    
    return True


def check_storage():
    """Check storage setup."""
    print("\n💾 Storage Check")
    print("=" * 40)
    
    workdrive_path = Path("/Volumes/WorkDrive")
    
    if not workdrive_path.exists():
        print("❌ WorkDrive not found at /Volumes/WorkDrive")
        print("   Please connect your external drive and name it 'WorkDrive'")
        return False
    
    print("✅ WorkDrive found")
    
    # Check available space
    stat = os.statvfs(str(workdrive_path))
    free_space_gb = (stat.f_bavail * stat.f_frsize) / (1024**3)
    total_space_gb = (stat.f_blocks * stat.f_frsize) / (1024**3)
    
    print(f"   Total space: {total_space_gb:.1f} GB")
    print(f"   Free space: {free_space_gb:.1f} GB")
    
    if free_space_gb < 30:
        print("⚠️  Warning: Less than 30GB free space")
        print("   You may need more space for models (~22GB total)")
    
    # Check directory structure
    required_dirs = [
        "models",
        "models/cogvideox-5b-weights",
        "models/cogvideox-vae", 
        "models/t5-v1_1-xxl",
        "cache",
        "output"
    ]
    
    for dir_name in required_dirs:
        dir_path = workdrive_path / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - Missing")
    
    return True


def check_models():
    """Check if models are downloaded."""
    print("\n🤖 Model Check")
    print("=" * 40)
    
    workdrive_path = Path("/Volumes/WorkDrive")
    
    models = {
        "CogVideoX-5B": {
            "path": workdrive_path / "models/cogvideox-5b-weights",
            "files": [
                "diffusion_pytorch_model-00001-of-00002.safetensors",
                "diffusion_pytorch_model-00002-of-00002.safetensors"
            ]
        },
        "CogVideoX VAE": {
            "path": workdrive_path / "models/cogvideox-vae",
            "files": ["diffusion_pytorch_model.safetensors"]
        },
        "T5-XXL": {
            "path": workdrive_path / "models/t5-v1_1-xxl",
            "files": [
                "pytorch_model-00001-of-00002.bin",
                "pytorch_model-00002-of-00002.bin"
            ]
        }
    }
    
    all_models_ready = True
    
    for model_name, model_info in models.items():
        model_path = model_info["path"]
        required_files = model_info["files"]
        
        if not model_path.exists():
            print(f"❌ {model_name} - Directory not found")
            all_models_ready = False
            continue
        
        missing_files = []
        for file_name in required_files:
            file_path = model_path / file_name
            if not file_path.exists():
                missing_files.append(file_name)
        
        if missing_files:
            print(f"❌ {model_name} - Missing files: {', '.join(missing_files)}")
            all_models_ready = False
        else:
            print(f"✅ {model_name}")
    
    if not all_models_ready:
        print("\n💡 To download models, run:")
        print("   python /Volumes/WorkDrive/download_models.py")
    
    return all_models_ready


def check_ttt_compatibility():
    """Check TTT layer compatibility."""
    print("\n🧠 TTT Layer Check")
    print("=" * 40)
    
    try:
        from ttt.infra.device_manager import get_device_manager
        from ttt.models.ssm.mac_ttt_layer import check_ttt_compatibility
        
        device_manager = get_device_manager()
        device, device_type = device_manager.get_optimal_device()
        
        print(f"Optimal device: {device} ({device_type})")
        
        # Check TTT compatibility
        check_ttt_compatibility()
        
        return True
        
    except ImportError as e:
        print(f"❌ TTT modules not found: {e}")
        return False
    except Exception as e:
        print(f"❌ TTT check failed: {e}")
        return False


def main():
    """Run all checks."""
    print("🍎 TTT-Video Mac Setup Test")
    print("=" * 50)
    
    checks = [
        ("System", check_system),
        ("Dependencies", check_dependencies), 
        ("PyTorch Backends", check_torch_backends),
        ("Storage", check_storage),
        ("Models", check_models),
        ("TTT Compatibility", check_ttt_compatibility)
    ]
    
    results = {}
    
    for check_name, check_func in checks:
        try:
            results[check_name] = check_func()
        except Exception as e:
            print(f"❌ {check_name} check failed: {e}")
            results[check_name] = False
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    all_passed = True
    for check_name, passed in results.items():
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {check_name}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL CHECKS PASSED!")
        print("You're ready to run TTT-Video on Mac!")
        print("\nNext steps:")
        print("1. Create input prompts: echo '{\"positive_prompt\": \"A cat\", \"negative_prompt\": \"\"}' > input.json")
        print("2. Run inference: python sample_mac.py --job.config_file configs/mac/mac_3s.toml --eval.input_file input.json")
    else:
        print("⚠️  SOME CHECKS FAILED")
        print("Please fix the issues above before running TTT-Video.")
    
    return all_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
