[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ttt-video"
version = "0.2.0"
description = "Training and sampling code for ttt-video"
keywords = ["test time training", "ttt", "training", "diffusion", "video"]
requires-python = ">=3.12"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON>" },
    { name = "<PERSON>" },
    { name = "<PERSON><PERSON>" } 
]
dependencies = [
    "aiohttp==3.11.11",
    "decord==0.6.0",
    "einops==0.8.0",
    "natsort==8.4.0",
    "pytorch-lightning==2.5.0.post0",
    "pytz==2024.2",
    "safetensors==0.4.5",
    "tomli==2.2.1",
    "torch==2.6.0",
    "torchvision==0.21.0",
    "triton==3.2.0",
    "xformers==0.0.29.post3",
    "tqdm==4.67.1",
    "pillow==11.0.0",
    "transformers==4.47.1",
    "imageio==2.36.1",
    "sentencepiece==0.2.0",
    "imageio==2.36.1",
    "imageio-ffmpeg==0.5.1",
    "wandb==0.19.1",
    "submitit==1.5.2",
    "scipy>=1.15.1",
    "tenacity==9.0.0",
]
	
[dependency-groups]
dev = [
    "black>=24.10.0",
    "flake8>=8.0.0",
    "isort>=5.13.2"
]

[tool.setuptools]
package-dir = {"" = "."}
packages = ["ttt"]

[tool.uv]
package = true
prerelease = "if-necessary-or-explicit"
default-groups = ["dev"]

[tool.black]
line-length = 120
